import os
from amis import reconstruct_multi_models

def parse_args():
    import argparse
    parser = argparse.ArgumentParser(
        description='Recommend substitutions to a wildtype sequence'
    )
    parser.add_argument('sequence', type=str,
                        help='Wildtype sequence')
    parser.add_argument(
        '--model-names',
        type=str,
        default=[ 'esm1b', 'esm1v1', 'esm1v2', 'esm1v3', 'esm1v4', 'esm1v5', 'esm2-8M', 'esm2-35M', 'esm2-150M', 'esm2-650M', 'igbert', ],
        nargs='+',
        help='Type of language model (e.g., esm1b, esm1v1, esm2-650M)'
    )
    parser.add_argument(
        '--alpha',
        type=float,
        default=None,
        help='alpha stringency parameter'
    )
    parser.add_argument(
        '--cuda',
        type=str,
        default='cuda',
        help='cuda device to use'
    )
    args = parser.parse_args()
    return args

if __name__ == '__main__':
    args = parse_args()

    if ":" in args.cuda:
        os.environ["CUDA_VISIBLE_DEVICES"] = args.cuda.split(':')[-1]

    mutations_models, mutations_model_names = reconstruct_multi_models(
        args.sequence,
        args.model_names,
        alpha=args.alpha,
        return_names=True,
    )
    for k, v in sorted(mutations_models.items(), key=lambda item: -item[1]):
        mut_str = f'{k[1]}{k[0] + 1}{k[2]}'
        models = ', '.join(mutations_model_names[k])
        print(f'{mut_str}\t{v}\t{models}')
