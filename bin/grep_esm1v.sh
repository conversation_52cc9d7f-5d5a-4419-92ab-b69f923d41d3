#!/bin/bash

# Set global variables
LOGS_DIR="$1"
ANTIBODY="$2"

# Check if grep supports Perl regex (-P)
if echo "test" | grep -P "test" &>/dev/null; then
    GREP_CMD="grep -h -P"
else
    # Fall back to basic grep with extended regex
    GREP_CMD="grep -h -E"
fi

# Function to process and format results
process_results() {
    local chain_type=$1
    local pattern=$2

    echo "===== $chain_type Mutations ====="
    echo "Mutation | # Models | Models"
    echo "---------|----------|-------"

    # Create a temporary file to store all mutations
    temp_file=$(mktemp)

    # Extract all mutations from all model files
    $GREP_CMD "$pattern" ${LOGS_DIR}/reconstruct_${ANTIBODY}_* > $temp_file

    # Process each mutation
    for mutation in $(cat $temp_file | cut -f2- -d':' | sed 's/,/\n/g' | sed 's/ //g' | sort | uniq); do
        # Count how many models recommend this mutation
        count=0
        models=""

        for model in esm1b esm1v1 esm1v2 esm1v3 esm1v4 esm1v5 esm2-8M esm2-35M esm2-150M esm2-650M igbert; do
            if grep -q "$mutation" "${LOGS_DIR}/reconstruct_${ANTIBODY}_$model.log"; then
                count=$((count+1))
                if [ -z "$models" ]; then
                    models="$model"
                else
                    models="$models, $model"
                fi
            fi
        done

        # Print the result in table format
        echo "$mutation | $count | $models"
    done | sort -t'|' -k2 -nr

    # Remove temporary file
    rm $temp_file

    echo ""
}

# Determine patterns based on antibody name
if [[ "$ANTIBODY" == "medi8852" ]]; then
    vh_pattern="MEDI VH"
    vl_pattern="MEDI VK"
elif [[ "$ANTIBODY" == "medi_uca" ]]; then
    vh_pattern="MEDI UCA VH"
    vl_pattern="MEDI UCA VK"
elif [[ "$ANTIBODY" == "mab114" ]]; then
    vh_pattern="mAb114 VH"
    vl_pattern="mAb114 VK"
elif [[ "$ANTIBODY" == "mab114_uca" ]]; then
    vh_pattern="mAb114 UCA VH"
    vl_pattern="mAb114 UCA VK"
elif [[ "$ANTIBODY" == "s309" ]]; then
    vh_pattern="S309 VH"
    vl_pattern="S309 VL"
elif [[ "$ANTIBODY" == "regn10987" ]]; then
    vh_pattern="REGN10987 VH"
    vl_pattern="REGN10987 VL"
elif [[ "$ANTIBODY" == "c143" ]]; then
    vh_pattern="C143 VH"
    vl_pattern="C143 VL"
else
    # Default patterns for unknown antibodies
    vh_pattern="VH"
    vl_pattern="VL\|VK"
fi

# Process VH mutations
process_results "VH" "$vh_pattern"

# Process VL/VK mutations
process_results "VL/VK" "$vl_pattern"
