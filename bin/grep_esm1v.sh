#!/bin/bash

# Check if grep supports Perl regex (-P)
if echo "test" | grep -P "test" &>/dev/null; then
    GREP_CMD="grep -h -P"
else
    # Fall back to basic grep with extended regex
    GREP_CMD="grep -h -E"
fi

# Function to process and format results
process_results() {
    local chain_type=$1
    local pattern=$2
    
    echo "===== $chain_type Mutations ====="
    echo "Mutation | # Models | Models"
    echo "---------|----------|-------"
    
    # Create a temporary file to store all mutations
    temp_file=$(mktemp)
    
    # Extract all mutations from all model files
    $GREP_CMD "$pattern" $3/reconstruct_* > $temp_file
    
    # Process each mutation
    for mutation in $(cat $temp_file | cut -f2- -d':' | sed 's/,/\n/g' | sed 's/ //g' | sort | uniq); do
        # Count how many models recommend this mutation
        count=0
        models=""
        
        for model in esm1b esm1v1 esm1v2 esm1v3 esm1v4 esm1v5; do
            if grep -q "$mutation" "$3/reconstruct_$model.log"; then
                count=$((count+1))
                if [ -z "$models" ]; then
                    models="$model"
                else
                    models="$models, $model"
                fi
            fi
        done
        
        # Print the result in table format
        echo "$mutation | $count | $models"
    done | sort -t'|' -k2 -nr
    
    # Remove temporary file
    rm $temp_file
    
    echo ""
}

# Process VH mutations
process_results "VH" "VH mut|MEDI VH|mAb114 VH|S309 VH|REGN10987 VH|C143 VH" $1

# Process VL/VK mutations
process_results "VL/VK" "VL mut|VK mut|MEDI VK|mAb114 VK|S309 VL|REGN10987 VL|C143 VL" $1
