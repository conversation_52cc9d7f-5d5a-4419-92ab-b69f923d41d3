import torch
import numpy as np
import warnings
from transformers import BertModel, BertTokenizer

class IgBertModel(object):
    def __init__(self, name='Exscientia/IgBert'):
        self.name_ = name
        
        with warnings.catch_warnings():
            warnings.simplefilter('ignore', UserWarning)
            self.tokenizer_ = BertTokenizer.from_pretrained(name, do_lower_case=False)
            self.model_ = BertModel.from_pretrained(name, add_pooling_layer=False)
        
        self.model_.eval()
        if torch.cuda.is_available():
            self.model_ = self.model_.cuda()
        
        # Create a simple alphabet-like interface for compatibility
        self.alphabet_ = SimpleAlphabet()
        self.offset_ = 1

    def predict_sequence_prob(self, seq):
        """
        For IgBert, we need to handle this differently since it expects paired sequences.
        For single sequence input, we'll treat it as a heavy chain and use a dummy light chain.
        """
        # For compatibility, treat single sequence as heavy chain with dummy light chain
        dummy_light = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"
        
        # Format as paired sequence
        paired_seq = ' '.join(seq) + ' [SEP] ' + ' '.join(dummy_light)
        
        tokens = self.tokenizer_.batch_encode_plus(
            [paired_seq], 
            add_special_tokens=True, 
            padding=True, 
            return_tensors="pt",
            return_special_tokens_mask=True
        )
        
        if torch.cuda.is_available():
            tokens = {k: v.cuda() for k, v in tokens.items()}
        
        with torch.no_grad():
            output = self.model_(
                input_ids=tokens['input_ids'], 
                attention_mask=tokens['attention_mask']
            )
            
            # Get embeddings for the heavy chain part only (before [SEP])
            embeddings = output.last_hidden_state[0]  # Remove batch dimension
            
            # Find the first [SEP] token to separate heavy and light chains
            sep_token_id = self.tokenizer_.sep_token_id
            sep_positions = (tokens['input_ids'][0] == sep_token_id).nonzero(as_tuple=True)[0]
            
            if len(sep_positions) > 0:
                # Take embeddings up to the first [SEP] (excluding [CLS])
                heavy_embeddings = embeddings[1:sep_positions[0]]  # Skip [CLS] token
            else:
                # Fallback: take all embeddings except special tokens
                heavy_embeddings = embeddings[1:-1]  # Skip [CLS] and [SEP]
            
            # Truncate or pad to match input sequence length
            if heavy_embeddings.shape[0] > len(seq):
                heavy_embeddings = heavy_embeddings[:len(seq)]
            elif heavy_embeddings.shape[0] < len(seq):
                # Pad with zeros if needed
                padding = torch.zeros(len(seq) - heavy_embeddings.shape[0], heavy_embeddings.shape[1])
                if torch.cuda.is_available():
                    padding = padding.cuda()
                heavy_embeddings = torch.cat([heavy_embeddings, padding], dim=0)
        
        return heavy_embeddings.cpu().numpy()

    def encode(self, seq):
        """
        Encode a sequence using IgBert embeddings
        """
        return self.predict_sequence_prob(seq)

    def decode(self, embedding):
        """
        For IgBert, we don't have a direct decode capability like ESM models.
        This is a placeholder that returns random logits for compatibility.
        """
        # Return random logits for compatibility (this won't be used for reconstruction)
        seq_len, embed_dim = embedding.shape
        vocab_size = len(self.alphabet_.all_toks)
        logits = np.random.randn(seq_len, vocab_size)
        return logits


class SimpleAlphabet(object):
    """
    Simple alphabet class for compatibility with ESM interface
    """
    def __init__(self):
        # Standard amino acid alphabet
        self.all_toks = [
            '<cls>', '<pad>', '<eos>', '<unk>',
            'L', 'A', 'G', 'V', 'S', 'E', 'R', 'T', 'I', 'D', 'P', 'K',
            'Q', 'N', 'F', 'Y', 'M', 'H', 'W', 'C', 'X', 'B', 'U', 'Z', 'O', '.', '-'
        ]
        self.tok_to_idx = {tok: i for i, tok in enumerate(self.all_toks)}
        self.padding_idx = self.tok_to_idx['<pad>']
        
    def get_idx(self, tok):
        return self.tok_to_idx.get(tok, self.tok_to_idx['<unk>'])
        
    def get_tok(self, idx):
        return self.all_toks[idx] if 0 <= idx < len(self.all_toks) else '<unk>'
