import torch
import numpy as np
import warnings
from transformers import <PERSON><PERSON><PERSON><PERSON>, Bert<PERSON>oken<PERSON>, BertForMaskedLM

class IgBertModel(object):
    def __init__(self, name='Exscientia/IgBert'):
        self.name_ = name

        with warnings.catch_warnings():
            warnings.simplefilter('ignore', UserWarning)
            self.tokenizer_ = BertTokenizer.from_pretrained(name, do_lower_case=False)
            self.model_ = BertForMaskedLM.from_pretrained(name)

        self.model_.eval()
        if torch.cuda.is_available():
            self.model_ = self.model_.cuda()

        # Create a simple alphabet-like interface for compatibility
        self.alphabet_ = SimpleAlphabet()
        self.offset_ = 1

    def predict_sequence_prob(self, seq, light_chain=None):
        """
        Use IgBert's masked language modeling to predict mutations.
        For single sequence input, we'll treat it as a heavy chain and use a dummy light chain.
        """
        if light_chain is None:
            # Use a canonical light chain sequence
            light_chain = "EVVMTQSPASLSVSPGERATLSCRARASLGISTDLAWYQQRPGQAPRLLIYGASTRATGIPARFSGSGSGTEFTLTISSLQSEDSAVYYCQQYSNWPLTFGGGTKVEIK"

        # Format as paired sequence with spaces between amino acids
        paired_seq = ' '.join(seq) + ' [SEP] ' + ' '.join(light_chain)

        # Tokenize the sequence
        tokens = self.tokenizer_.batch_encode_plus(
            [paired_seq],
            add_special_tokens=True,
            padding=True,
            return_tensors="pt",
            return_special_tokens_mask=True
        )

        if torch.cuda.is_available():
            tokens = {k: v.cuda() for k, v in tokens.items()}

        # Get logits for all positions
        with torch.no_grad():
            output = self.model_(
                input_ids=tokens['input_ids'],
                attention_mask=tokens['attention_mask']
            )
            logits = output.logits[0]  # Remove batch dimension

            # Find the first [SEP] token to separate heavy and light chains
            sep_token_id = self.tokenizer_.sep_token_id
            sep_positions = (tokens['input_ids'][0] == sep_token_id).nonzero(as_tuple=True)[0]

            if len(sep_positions) > 0:
                # Take logits for heavy chain only (excluding [CLS] and up to first [SEP])
                heavy_logits = logits[1:sep_positions[0]]  # Skip [CLS] token
            else:
                # Fallback: take all logits except special tokens
                heavy_logits = logits[1:-1]  # Skip [CLS] and [SEP]

            # Truncate or pad to match input sequence length
            if heavy_logits.shape[0] > len(seq):
                heavy_logits = heavy_logits[:len(seq)]
            elif heavy_logits.shape[0] < len(seq):
                # Pad with zeros if needed
                vocab_size = heavy_logits.shape[1]
                padding = torch.zeros(len(seq) - heavy_logits.shape[0], vocab_size)
                if torch.cuda.is_available():
                    padding = padding.cuda()
                heavy_logits = torch.cat([heavy_logits, padding], dim=0)

        return heavy_logits.cpu().numpy()

    def encode(self, seq):
        """
        Encode a sequence using IgBert embeddings
        """
        return self.predict_sequence_prob(seq)

    def decode(self, logits):
        """
        Convert IgBert logits to amino acid sequence.
        """
        # Map IgBert tokenizer vocabulary to amino acids
        amino_acids = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I',
                      'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V']

        # Get token IDs for amino acids
        aa_token_ids = []
        for aa in amino_acids:
            token_id = self.tokenizer_.convert_tokens_to_ids(aa)
            aa_token_ids.append(token_id)

        # Extract logits for amino acid tokens only
        aa_logits = logits[:, aa_token_ids]

        # Get the most likely amino acid for each position
        predicted_indices = np.argmax(aa_logits, axis=1)
        predicted_sequence = ''.join([amino_acids[idx] for idx in predicted_indices])

        return predicted_sequence




class SimpleAlphabet(object):
    """
    Simple alphabet class for compatibility with ESM interface
    """
    def __init__(self):
        # Standard amino acid alphabet
        self.all_toks = [
            '<cls>', '<pad>', '<eos>', '<unk>',
            'L', 'A', 'G', 'V', 'S', 'E', 'R', 'T', 'I', 'D', 'P', 'K',
            'Q', 'N', 'F', 'Y', 'M', 'H', 'W', 'C', 'X', 'B', 'U', 'Z', 'O', '.', '-'
        ]
        self.tok_to_idx = {tok: i for i, tok in enumerate(self.all_toks)}
        self.padding_idx = self.tok_to_idx['<pad>']

    def get_idx(self, tok):
        return self.tok_to_idx.get(tok, self.tok_to_idx['<unk>'])

    def get_tok(self, idx):
        return self.all_toks[idx] if 0 <= idx < len(self.all_toks) else '<unk>'
